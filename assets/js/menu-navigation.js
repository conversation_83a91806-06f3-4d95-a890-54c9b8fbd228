class MenuNavigationHandler {
    constructor() {
        this.menuItems = [];
        this.sections = [];
        this.isScrolling = false;
        this.scrollTimeout = null;
        
        this.init();
    }

    init() {

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.collectMenuItems();
        this.collectSections();
        this.bindEvents();
        this.checkInitialState();
    }

    collectMenuItems() {
        this.menuItems = Array.from(document.querySelectorAll('.e-n-menu-title'));
    }

    collectSections() {
        this.sections = [];


        const anchorLinks = this.menuItems
            .map(item => item.querySelector('a'))
            .filter(link => link && link.getAttribute('href')?.startsWith('#'));

        anchorLinks.forEach(link => {
            const href = link.getAttribute('href');
            const id = href.substring(1);
            const element = document.getElementById(id);
            const menuItem = link.closest('.e-n-menu-title');

            if (element && menuItem) {
                this.sections.push({
                    id,
                    element,
                    menuItem,
                    link,
                    href
                });
            }
        });


        this.sections.sort((a, b) => {
            const aTop = a.element.getBoundingClientRect().top + window.scrollY;
            const bTop = b.element.getBoundingClientRect().top + window.scrollY;
            return aTop - bTop;
        });
    }

    bindEvents() {

        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        

        this.menuItems.forEach(item => {
            const link = item.querySelector('a');
            if (link && link.getAttribute('href')?.startsWith('#')) {
                link.addEventListener('click', this.handleMenuClick.bind(this, item, link));
            }
        });


        window.addEventListener('popstate', () => {
            setTimeout(() => this.checkInitialState(), 100);
        });
    }

    handleScroll() {
        if (this.isScrolling) return;
        
        this.isScrolling = true;
        requestAnimationFrame(() => {
            this.checkActiveSection();
            this.isScrolling = false;
        });
    }

    handleMenuClick(menuItem, link, event) {
        const targetId = link.getAttribute('href').substring(1);
        const targetSection = document.getElementById(targetId);
        
        if (!targetSection) return;
        
        event.preventDefault();
        

        targetSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        

        setTimeout(() => {
            this.setActiveMenuItem(menuItem);
        }, 150);
    }

    clearActiveStates() {
        this.menuItems.forEach(item => {
            item.classList.remove('menu-active');
        });
    }

    setActiveMenuItem(activeItem) {
        this.clearActiveStates();
        if (activeItem) {
            activeItem.classList.add('menu-active');
        }
    }

    checkCurrentPage() {
        const currentPath = window.location.pathname;
        

        if (currentPath.includes('/oferta') || currentPath.endsWith('/oferta/')) {
            const offerMenuItem = document.querySelector('a[href*="/oferta"]')?.closest('.e-n-menu-title');
            if (offerMenuItem) {
                this.setActiveMenuItem(offerMenuItem);
                return true;
            }
        }
        
        return false;
    }

    checkActiveSection() {

        if (this.checkCurrentPage()) {
            return;
        }

        const scrollPosition = window.scrollY + window.innerHeight * 0.2;
        let activeSection = null;


        for (let i = 0; i < this.sections.length; i++) {
            const section = this.sections[i];
            if (!section.element) continue;

            const rect = section.element.getBoundingClientRect();
            const sectionTop = window.scrollY + rect.top;
            const sectionBottom = sectionTop + rect.height;


            if (sectionTop <= scrollPosition && sectionBottom > scrollPosition) {
                activeSection = section;
                break;
            }
        }


        if (!activeSection) {

            if (window.scrollY < 100 && this.sections.length > 0) {
                activeSection = this.sections[0];
            }

            else if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 100 && this.sections.length > 0) {
                activeSection = this.sections[this.sections.length - 1];
            }
        }


        if (activeSection && activeSection.menuItem) {
            this.setActiveMenuItem(activeSection.menuItem);
        } else if (!this.checkCurrentPage()) {

            this.clearActiveStates();
        }
    }

    checkInitialState() {

        const hash = window.location.hash;
        if (hash) {

            const menuItem = this.sections.find(section => section.href === hash)?.menuItem;

            if (menuItem) {
                this.setActiveMenuItem(menuItem);
                return;
            }
        }


        if (!this.checkCurrentPage()) {

            this.checkActiveSection();
        }
    }


    refresh() {
        this.collectMenuItems();
        this.collectSections();
        this.checkInitialState();
    }

    debug() {
        console.group('Menu Navigation Debug');
        console.log('Znalezione elementy menu:', this.menuItems.length);
        console.log('Znalezione sekcje:', this.sections.length);

        this.sections.forEach((section, index) => {
            console.log(`${index + 1}. Sekcja "${section.id}":`, {
                element: section.element,
                menuItem: section.menuItem,
                href: section.href,
                position: section.element.getBoundingClientRect().top + window.scrollY
            });
        });

        const currentActive = document.querySelector('.e-n-menu-title.menu-active');
        console.log('Aktualnie aktywny element:', currentActive);
        console.groupEnd();
    }
}


const menuNavigation = new MenuNavigationHandler();


if (typeof window !== 'undefined') {
    window.MenuNavigationHandler = MenuNavigationHandler;
    window.menuNavigation = menuNavigation;
}
